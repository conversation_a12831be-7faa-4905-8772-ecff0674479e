package com.ecco.webApi.featureConfig;

import com.ecco.config.dom.*;
import com.ecco.config.repositories.FormDefinitionRepository;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.config.repositories.SettingsRepository;
import com.ecco.config.repositories.SoftwareFeatureRepository;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.dao.*;
import com.ecco.dom.agreements.AppointmentTypeRepository;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.User;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.*;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.service.SessionDataService;
import com.ecco.serviceConfig.viewModel.*;
import com.ecco.webApi.contacts.IndividualUserSummaryToViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.evidence.ExtractCommandViewModelJson;
import com.ecco.webApi.forms.FormDefinitionToViewModel;
import com.ecco.webApi.listsConfig.*;
import com.ecco.webApi.serviceConfig.TaskDefinitionToViewModel;
import com.ecco.webApi.serviceConfig.ServiceToViewModel;
import com.ecco.webApi.serviceConfig.ServiceViewModel;
import com.ecco.webApi.support.EtaggedResponseCacheManager;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Multimaps;
import com.ecco.calendar.core.CalendarService;
import lombok.RequiredArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ecco.config.dom.ListDefinitionEntry.extractListName;
import static com.ecco.security.SecurityUtil.hasAuthority;
import static com.ecco.webApi.featureConfig.Messages.EXPOSED_MESSAGE_KEYS;
import static java.util.Comparator.comparing;
import static java.util.Comparator.nullsLast;
import static java.util.stream.Collectors.toList;

@RestController
@RequiredArgsConstructor
@Secured("ROLE_USER")
// TODO: we need to sort out what we're doing when we impersonate user .. i.e. @RequestMapping should include username
public class SessionDataController extends BaseWebApiController {

    private final AppointmentTypeToViewModel apptTypeToViewModel = new AppointmentTypeToViewModel();
    private final TaskDefinitionToViewModel taskDefinitionToViewModel = new TaskDefinitionToViewModel();
    private final FundingSourceToViewModel fundingSourceToViewModel = new FundingSourceToViewModel();
    private final FeatureVoteToViewModel featureVoteToViewModel = new FeatureVoteToViewModel();
    private final SoftwareModuleToViewModel softwareModuleToViewModel = new SoftwareModuleToViewModel();
    private final ProjectToViewModel projectToViewModel = new ProjectToViewModel();

    private final SessionDataService sessionDataService;
    private final SoftwareModuleService softwareModuleService;
    private final SoftwareFeatureRepository softwareFeatureRepository;
    private final ListDefinitionRepository listDefinitionRepository;
    private final EntityRestrictionService entityRestrictionService;
    private final ConfigCommandRepository configCommandRepository;
    private final SettingsRepository settingsRepository;
    private final TaskDefinitionRepository taskDefinitionRepository;
    private final ObjectMapper objectMapper;
    private final AppointmentTypeRepository appointmentTypeRepository;
    private final ListDefCommandHandler listDefCommandHandler;
    private final FundingSourceRepository fundingSourceRepository;
    private final ServiceRepository serviceRepository;
    private final ProjectRepository projectRepository;
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private final RepositoryBasedServiceCategorisationService serviceCategorisationService;
    private final EtaggedResponseCacheManager etaggedResponseCacheManager;
    private final ServiceTypeService serviceTypeService;
    private final CalendarService calendarService;
    private final IndividualUserSummaryToViewModel individualUserSummaryToViewModel;
    private final ExtractCommandViewModelJson<ConfigCommand> extractJsonBody;
    private final MessageSource messageSource;
    private final ServiceCategorisationToViewModel serviceCategorisationToViewModel;
    private ServiceToViewModel serviceToViewModel;

    private final FormDefinitionRepository formDefinitionRepository;
    private final FormDefinitionToViewModel formDefinitionToDto = new FormDefinitionToViewModel();


    @PostConstruct
    public void init() {
        serviceToViewModel = new ServiceToViewModel(new ServiceCategorisationToProjectViewModel());
    }

    // see also InboundBaseController
    @PreAuthorize("permitAll()")
    @GetJson("/config/global")
    @ReadOnlyTransaction(timeout = 120)
    public void findGlobalConfig(WebRequest request, HttpServletResponse response) throws IOException {

        etaggedResponseCacheManager.getFromCacheWithEtagHandling(request, response,
                "globalConfig", "all", 3600,  // TODO: Reduce to 5 mins when we've eliminated duplicate config apis such as api/service/
                () -> {
            GlobalConfigViewModel vm = new GlobalConfigViewModel();
            globalSessionData(vm);
            return vm;
        });
    }

    @GetJson("/config/user/")
    @ReadOnlyTransaction
    public UserSessionDataViewModel findUserData(Authentication auth, HttpServletResponse response) {
        UserSessionDataViewModel result = new UserSessionDataViewModel();
        userSessionData(auth, result);
        // Cache for less time if we are are someone who can switch roles
        var cacheSeconds = hasAuthority("ROLE_SWITCHUSER") || hasAuthority("ROLE_PREVIOUS_ADMINISTRATOR") ? 5 : 300; // 5 seconds or 5 minutes for our own user details
        cacheForXSecs(cacheSeconds, response); // FIXME: Could cache for longer, but we need a way to get user config as config/user/{username} as cache busting per user
        return result;
    }

    private void globalSessionData(GlobalConfigViewModel result) {
        addGlobalFeaturesVotes(result);
        addGlobalSoftwareModules(result);

        // NB this doesn't seem to reload question choices/frees after a cache reset/reload
        //      debug sql output to console and search 'questionanswerchoices' after a reset/reload
        // Yet, after a restart, the WorkflowController reloads them via serviceTypeService.findOneDto
        // So, we attempt to load the questions above first...
        // it probably has something to do with these type of logs:
        //      HHH90001006: Missing cache[com.ecco.serviceConfig.dom.Question.choices] was created on-the-fly. The created cache will use a provider-specific default configuration: make sure you defined one. You can disable this warning by setting 'hibernate.javax.cache.missing_cache_strategy' to 'create'.
        result.serviceTypesById = serviceTypeService.findAllIds().stream()
                .map(id -> serviceTypeService.findOneDto(id.intValue()))
                .collect(Collectors.toMap(vm -> vm.id, vm -> vm));
        result.formDefinitionsById = formDefinitionRepository.findAll().stream()
                .map(formDefinitionToDto)
                .collect(Collectors.toMap(dto -> dto.uuid.toString(), dto -> dto));
        result.services = serviceRepository.findAll().stream().map(serviceToViewModel)
                .sorted(Comparator.comparing(ServiceViewModel::getName))
                .collect(toList());
        result.projects = projectRepository.findAll().stream().map(projectToViewModel)
                .sorted(Comparator.comparing(ProjectViewModel::getName))
                .collect(toList());
        result.serviceCategorisations = serviceCategorisationRepository.findAll().stream().map(serviceCategorisationToViewModel).collect(toList());


        result.taskDefinitions = taskDefinitionRepository.findAll().stream()
                .map(taskDefinitionToViewModel)
                .collect(toList());

        // LIST DEFs
        // see also ListDefinitionController
        Stream<ListDefinitionEntry> all = listDefinitionRepository.findAll().stream()
                .sorted(comparing(ListDefinitionEntry::getListName, String.CASE_INSENSITIVE_ORDER)
                        .thenComparing(ListDefinitionEntry::getOrder, nullsLast(Comparator.naturalOrder()))
                        .thenComparing(ListDefinitionEntry::getName, String.CASE_INSENSITIVE_ORDER));
        ImmutableListMultimap<String, ListDefinitionEntry> index = Multimaps.index(all.collect(toList()), extractListName);
        result.listDefinitions = Multimaps.transformValues(index, ListDefinitionEntryToViewModel::apply).asMap();

        // NOT LIST DEFs - also regions?
        result.appointmentTypes = appointmentTypeRepository.findAll().stream().map(apptTypeToViewModel)
                .sorted(Comparator.comparing(AppointmentTypeViewModel::getName))
                .collect(toList());
        result.fundingSources = fundingSourceRepository.findAll().stream().map(fundingSourceToViewModel)
                .sorted(Comparator.comparing(FundingSourceViewModel::getName))
                .collect(toList());
        result.supportOutcomes = sessionDataService.findOutcomes();
        result.riskAreas = sessionDataService.findRiskAreas();
        result.questionGroups = sessionDataService.findQuestionGroups();

        // we can't use toMap to simplify this code, since a possible NULL setting value causes NPE
        // see http://stackoverflow.com/a/24634007
        result.settings = settingsRepository.findAll().stream()
                .filter(this::isWhitelistedSetting)
                .collect(HashMap::new,
                        (map,setting) -> map.put(setting.getNamespace() + ":" + setting.getKey(), setting.getValue()),
                        HashMap::putAll);

        result.messages = generatePropertyMap();
    }

    private Map<String, String> generatePropertyMap() {
        Map<String, String> properties = new HashMap<>();
        for (String messageKey: EXPOSED_MESSAGE_KEYS) {
            properties.put(messageKey, messageSource.getMessage(messageKey, null, "", Locale.ROOT));
        }
        return properties;
    }

    private void userSessionData(Authentication auth, UserSessionData result) {
        result.setUsername(auth.getName());
        User user = SecurityUtil.getAuthenticatedUser();
        result.setUserId(user.getId());

        IndividualUserSummaryViewModel summary = individualUserSummaryToViewModel.apply(user.buildIndividualUserSummary());
        result.setIndividualUserSummary(summary);

        result.setCalendarId(user.getContact().getCalendarId());
        result.setCalendarIdUserReferenceUri(calendarService.findCalendarIdUserReferenceUri(user.getContact().getCalendarId()));

        addGroups(result, user);
        addRoles(result, auth);

        ServicesProjectsDto restrictions = EntityRestrictionService.getRestrictedServicesProjectsDto(entityRestrictionService, serviceCategorisationService);
        var restrictedIds = restrictions.getRestrictedServiceCategorisationIds().stream().map(ServiceCategorisationAclId::getId).toList();
        var restricted = serviceCategorisationRepository.findAll().stream()
                .filter(sc -> restrictedIds.contains(sc.getId()))
                .map(serviceCategorisationToViewModel)
                .collect(toList());
        restricted.sort(
                comparing(ServiceCategorisationViewModel::getServiceName, String.CASE_INSENSITIVE_ORDER)
                .thenComparing(ServiceCategorisationViewModel::getProjectName, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER))
        );
        // ?? unique
        result.setRestrictedServiceCategorisations(restricted);
    }

    private boolean isWhitelistedSetting(Setting setting) {
        switch (setting.getKey()) {
            case "LOGO_FILE_ID":
            case "comment.instructions":
            case "pageSize.history":
            case "care.scheduler.days":
            case "CLIENTCONTACT_OPTIONAL_FIELDS":
            case "CLIENTCONTACT_REQUIRED_FIELDS":
            case "CLIENT_DETAIL_OPTIONAL_FIELDS":
            case "CLIENT_DETAIL_REQUIRED_FIELDS":
            case "maxRecurringAllocateMonths":
                return true;

            default:
                return false;
        }
    }

    /** Allow admin to see changes to config */
    @Secured("ROLE_ADMIN")
    @GetJson("/config/commands")
    public String getConfigCommands() {
        var commands = configCommandRepository.findAll();
        return extractJsonBody.asJsonArray(commands);
    }

    /** Allow commands to be posted */
    @Secured("ROLE_ADMIN")
    @PostJson("/feature-config/")
    public Result updateAction(
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws IOException {

        FeatureVoteChangeCommandViewModel viewModel = objectMapper.readValue(requestBody, FeatureVoteChangeCommandViewModel.class);

        ConfigCommand duplicate = configCommandRepository.findOneByUuid(viewModel.uuid);

        if (duplicate != null) {
            return new Result("command ignored", duplicate.getId());
        }

        long userId = SecurityUtil.getUser(authentication).getId();
        UpdateFeatureVoteCommand savedCommand = configCommandRepository.save(
                new UpdateFeatureVoteCommand(
                        viewModel.uuid,
                        viewModel.timestamp,
                        userId,
                        requestBody));

        softwareFeatureRepository.setDefaultVote(viewModel.name, viewModel.voteChange.to);

        return new Result(Result.COMMAND_APPLIED, savedCommand.getId());
    }

    @Secured({"ROLE_ADMIN", "ROLE_ADMINGROUPSUPPORT"})
    @PostJson("/feature-config/listDef/")
    @ResponseStatus(HttpStatus.OK)
    public Result processListDefCommand(
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws IOException {

        return listDefCommandHandler.handleCommand(authentication, null, requestBody);
    }

    private void addGroups(UserSessionData result, User user) {
        result.setGroups(user.getGroups());
    }

    private void addRoles(UserSessionData result, Authentication auth) {
        result.setRoles(auth.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .toArray(String[]::new));
    }

    private void addGlobalFeaturesVotes(GlobalConfigViewModel result) {
        FeatureSetViewModel global = new FeatureSetViewModel();
        Iterable<SoftwareFeature> defaultFeatures = softwareFeatureRepository.findAll();

        for (SoftwareFeature softwareFeature : defaultFeatures) {
            global.featureVotes.put(softwareFeature.getName(), this.featureVoteToViewModel.apply(softwareFeature));
        }
        result.featureSets.put("global", global);
    }

    private void addGlobalSoftwareModules(GlobalConfigViewModel result) {
        Map<String, SoftwareModule> softwareModulesEnabled = softwareModuleService.getEnabledModules();
        for (String softwareModuleName: softwareModulesEnabled.keySet()) {
            result.softwareModulesEnabled.put(softwareModuleName, this.softwareModuleToViewModel.apply(softwareModulesEnabled.get(softwareModuleName)));
        }
    }
}
